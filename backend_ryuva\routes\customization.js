import express from 'express';
import {
  getCustomizationOptions,
  calculateCustomizationPrice,
  validateCustomization,
  getPopularCustomizations
} from '../controllers/customizationController.js';
import { param, body } from 'express-validator';
import { validate } from '../middleware/validation.js';

const router = express.Router();

// Validation rules
const productIdValidation = [
  param('productId')
    .isMongoId()
    .withMessage('Invalid product ID')
];

const calculatePriceValidation = [
  param('productId')
    .isMongoId()
    .withMessage('Invalid product ID'),
  body('customizations')
    .isArray({ min: 1 })
    .withMessage('At least one customization is required'),
  body('customizations.*.type')
    .isIn(['monogram', 'embroidery', 'fabric-upgrade'])
    .withMessage('Invalid customization type'),
  body('customizations.*.details')
    .isObject()
    .withMessage('Customization details are required')
];

const validateCustomizationValidation = [
  param('productId')
    .isMongoId()
    .withMessage('Invalid product ID'),
  body('customization.type')
    .isIn(['monogram', 'embroidery', 'fabric-upgrade'])
    .withMessage('Invalid customization type'),
  body('customization.details')
    .isObject()
    .withMessage('Customization details are required'),
  body('customization.details.text')
    .if(body('customization.type').equals('monogram'))
    .trim()
    .isLength({ min: 1, max: 10 })
    .withMessage('Monogram text must be between 1 and 10 characters'),
  body('customization.details.font')
    .if(body('customization.type').equals('monogram'))
    .notEmpty()
    .withMessage('Font selection is required for monogram'),
  body('customization.details.color')
    .if(body('customization.type').equals('monogram'))
    .notEmpty()
    .withMessage('Color selection is required for monogram'),
  body('customization.details.position')
    .if(body('customization.type').equals('monogram'))
    .notEmpty()
    .withMessage('Position selection is required for monogram')
];

// Public routes
router.get('/popular', getPopularCustomizations);
router.get('/:productId', productIdValidation, validate, getCustomizationOptions);
router.post('/:productId/calculate', calculatePriceValidation, validate, calculateCustomizationPrice);
router.post('/:productId/validate', validateCustomizationValidation, validate, validateCustomization);

export default router;
