// class Person{
//     constructor(name, phn_no, address){
//         this.name = name;
//         this.contact = phn_no;
//         this.address = address;
//     }
//     greet(){
//         console.log(`Hey, This is ${this.name}. My contact number is ${this.contact} and my address is ${this.address}`)
//     }
// }

// const person1 = new Person("<PERSON><PERSON><PERSON><PERSON><PERSON>","7992304866","<PERSON><PERSON> k<PERSON><PERSON> bhandar,purani bazar,jamui");

// person1.greet()


// //promise is evential completion(or failure) of an asynchorous operation
// // improves code readability
// // chaining of asynchrnous operations
// // error handling for potential issues during asynchronous execution.

// async function getCatFacts(){
//     try{
// const response = await fetch("https://catfact.ninja/fact");
// const data = await response.json()
// console.log(`\n\nCat Fact of the day is:\n \t${data.fact}`)
// }catch(err){
//     console.log(`Catched error is ${err}`)
// }
// }

// getCatFacts()

// var a=2
// a3()

// var a1 = function(){
//     console.log("hello x")
// }
// var a2 = ()=>{
//     console.log("hello y")
// }

// function a3(){
//     console.log("hello z")
// }
// a1()
// a2()



//props in reactjs: its building blocks of communication across components in react
