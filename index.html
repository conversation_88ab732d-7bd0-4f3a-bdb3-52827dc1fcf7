<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ryuva - Where Culture Meets Class</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
     <script defer>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        serif: ['Playfair Display', 'serif'],
                    },
                    colors: {
                        'ryuva-gold': '#B08D57', /* A muted, elegant gold */
                        'ryuva-deep-green': '#2E4034', /* A sophisticated deep green */
                        'ryuva-cream': '#FDFBF5', /* Slightly warmer cream for backgrounds */
                        'ryuva-charcoal': '#36454F', /* A soft charcoal for text */
                        'ryuva-light-gray': '#E5E7EB', /* For subtle borders or backgrounds */
                    }
                }
            }
        }
        </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Custom Tailwind configuration */
      

        /* Smooth scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Simple fade-in animation for sections on scroll */
        .scroll-animate {
            opacity: 1;
            transform: translateY(30px); /* Slightly increased for more noticeable effect */
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }

        .scroll-animate.in-view {
            opacity: 1;
            transform: translateY(0);
        }

        /* Styling for testimonial navigation (if you implement a JS slider) */
        .testimonial-slider-nav button {
            /* Basic styling for potential slider buttons */
            background-color: rgba(0,0,0,0.3);
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            transition: background-color 0.3s;
        }
        .testimonial-slider-nav button:hover {
            background-color: rgba(0,0,0,0.5);
        }
          .scroll-animate.animate { opacity: 1; transform: translateY(0); transition: opacity 0.8s ease-out, transform 0.8s ease-out; }
 
    </style>
</head>
<body class="font-sans bg-ryuva-cream text-ryuva-charcoal antialiased">
    <nav class="bg-white/90 backdrop-blur-lg shadow-md fixed w-full z-50 top-0 transition-all duration-300">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="flex-shrink-0">
                    <a href="#home" class="text-4xl font-serif font-bold text-ryuva-deep-green hover:text-ryuva-gold transition-colors">Ryuva</a>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#about" class="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">About</a>
                    <a href="#collections" class="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">Collections</a>
                    <a href="#why-ryuva" class="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">Why Ryuva?</a>
                    <a href="#contact" class="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">Contact</a>
                    <a href="#shop" class="ml-4 bg-ryuva-gold text-white py-2.5 px-6 rounded-lg hover:bg-opacity-80 transition-all duration-300 ease-in-out shadow-sm font-semibold text-sm">Shop Now</a>
                </div>
                <div class="md:hidden">
                    <button id="mobile-menu-button" type="button" class="inline-flex items-center justify-center p-2 rounded-md text-ryuva-charcoal hover:text-ryuva-gold hover:bg-ryuva-light-gray focus:outline-none focus:ring-2 focus:ring-inset focus:ring-ryuva-gold" aria-controls="mobile-menu" aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                        <svg class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="#about" class="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">About</a>
                <a href="#collections" class="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">Collections</a>
                <a href="#why-ryuva" class="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">Why Ryuva?</a>
                <a href="#contact" class="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">Contact</a>
                <a href="#shop" class="bg-ryuva-gold text-white block w-full text-center mt-2 py-2.5 px-6 rounded-lg hover:bg-opacity-80 transition-all duration-300 ease-in-out shadow-sm font-semibold text-sm">Shop Now</a>
            </div>
        </div>
    </nav>

    <header id="home" class="min-h-screen bg-cover bg-center flex items-center justify-center relative pt-20" style="background-image: url('https://placehold.co/1920x1080/2E4034/FDFBF5?text=Ryuva%3A+Elegance+for+All&font=playfairdisplay');">
        <div class="absolute inset-0 bg-gradient-to-t from-ryuva-deep-green/50 via-transparent to-transparent"></div>
        <div class="absolute inset-0 bg-black/30"></div>
        <div class="text-center z-10 p-6 max-w-3xl mx-auto">
            <h1 class="text-5xl sm:text-6xl md:text-7xl font-serif font-bold text-white mb-6 leading-tight shadow-text">Ryuva</h1>
            <p class="text-xl sm:text-2xl md:text-3xl font-serif text-gray-100 mb-10 shadow-text">"Where Culture Meets Class"</p>
            <div class="space-y-4 sm:space-y-0 sm:flex sm:flex-wrap sm:justify-center sm:gap-4">
                <a href="#shop" class="inline-block bg-ryuva-gold text-white py-3 px-8 rounded-lg text-lg font-semibold hover:bg-opacity-80 transition-all duration-300 ease-in-out transform hover:scale-105 shadow-xl">Shop Now</a>
                <a href="#customize" class="inline-block border-2 border-ryuva-gold text-white py-3 px-8 rounded-lg text-lg font-semibold hover:bg-ryuva-gold hover:text-white transition-all duration-300 ease-in-out transform hover:scale-105 shadow-xl">Customize Yours</a>
                <a href="#collections" class="inline-block border-2 border-gray-200 text-white py-3 px-8 rounded-lg text-lg font-semibold hover:bg-gray-200 hover:text-ryuva-charcoal transition-all duration-300 ease-in-out transform hover:scale-105 shadow-xl mt-4 sm:mt-0">Explore Collection</a>
            </div>
        </div>
    </header>

    <section id="about" class="py-16 md:py-24 bg-white scroll-animate">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="rounded-lg overflow-hidden shadow-2xl aspect-w-4 aspect-h-3">
                    <img src="https://placehold.co/600x450/FDFBF5/2E4034?text=Craftsmanship+%26+Modernity&font=playfairdisplay" alt="Handcrafting a Ryuva rumaal with modern elegance" class="w-full h-full object-cover">
                </div>
                <div class="prose prose-lg max-w-none text-ryuva-charcoal">
                    <h2 class="text-4xl font-serif font-bold text-ryuva-deep-green mb-6">The Essence of Ryuva</h2>
                    <p class="leading-relaxed mb-6">
                        Ryuva was born from a desire to bridge generations – to take the timeless elegance of the traditional rumaal and reimagine it for today's discerning individual. We saw beauty in the intricate artistry passed down through families and the vibrant energy of modern style.
                    </p>
                    <p class="leading-relaxed mb-8">
                        Each Ryuva piece is more than an accessory; it's a conversation starter, a piece of heritage handcrafted with meticulous care. We blend age-old cultural motifs with contemporary designs, ensuring every rumaal carries a personal touch and tells a unique story of sophistication.
                    </p>
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-ryuva-gold mr-4 flex-shrink-0 mt-1">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 14.188V12.008L18.25 12zm-2.25 0h.008v.008h-.008V12zm.75 2.25l.813 2.846H15L14.187 14.25l-2.846.813a4.5 4.5 0 00-3.09-3.09L5.25 9l2.846-.813a4.5 4.5 0 003.09-3.09L12 1.25l.813 2.846a4.5 4.5 0 003.09 3.09l2.846.813L14.25 9l.813 2.846a4.5 4.5 0 003.09 3.09L21.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
                            </svg>
                            <div>
                                <h3 class="font-semibold text-ryuva-deep-green text-xl">Handcrafted Detail</h3>
                                <p>Every stitch, every fold, a testament to artisanal skill.</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-ryuva-gold mr-4 flex-shrink-0 mt-1">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />
                            </svg>
                            <div>
                                <h3 class="font-semibold text-ryuva-deep-green text-xl">Cultural Motifs</h3>
                                <p>Inspired by rich traditions, designed for modern sensibilities.</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-ryuva-gold mr-4 flex-shrink-0 mt-1">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                            </svg>
                            <div>
                                <h3 class="font-semibold text-ryuva-deep-green text-xl">Personal Touch</h3>
                                <p>From monograms to bespoke designs, make it uniquely yours.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="collections" class="py-16 md:py-24 bg-ryuva-cream scroll-animate">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-serif font-bold text-ryuva-deep-green text-center mb-16">Discover Our Collections</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
                    <img src="https://placehold.co/400x300/B08D57/FFFFFF?text=Heritage+Rumaal&font=playfairdisplay" alt="Heritage Rumaals" class="w-full h-56 object-cover">
                    <div class="p-6 flex flex-col flex-grow">
                        <h3 class="text-2xl font-serif font-semibold text-ryuva-deep-green mb-2">Heritage Rumaals</h3>
                        <p class="text-ryuva-charcoal text-sm mb-4 flex-grow">Timeless designs celebrating rich cultural patterns and artisanal craftsmanship.</p>
                        <a href="#" class="mt-auto self-start text-ryuva-gold font-semibold hover:underline">Explore Heritage <span aria-hidden="true">&rarr;</span></a>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
                    <img src="https://placehold.co/400x300/36454F/FFFFFF?text=Minimal+Luxe&font=playfairdisplay" alt="Minimal Luxe Rumaals" class="w-full h-56 object-cover">
                    <div class="p-6 flex flex-col flex-grow">
                        <h3 class="text-2xl font-serif font-semibold text-ryuva-deep-green mb-2">Minimal Luxe</h3>
                        <p class="text-ryuva-charcoal text-sm mb-4 flex-grow">Clean lines, subtle textures, and sophisticated simplicity for the modern minimalist.</p>
                        <a href="#" class="mt-auto self-start text-ryuva-gold font-semibold hover:underline">Shop Minimal Luxe <span aria-hidden="true">&rarr;</span></a>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
                    <img src="https://placehold.co/400x300/2E4034/FFFFFF?text=Wedding+Gifting&font=playfairdisplay" alt="Wedding Gifting Rumaals" class="w-full h-56 object-cover">
                    <div class="p-6 flex flex-col flex-grow">
                        <h3 class="text-2xl font-serif font-semibold text-ryuva-deep-green mb-2">Wedding Gifting</h3>
                        <p class="text-ryuva-charcoal text-sm mb-4 flex-grow">Elegant rumaals perfect for memorable wedding favors and bridal party gifts.</p>
                        <a href="#" class="mt-auto self-start text-ryuva-gold font-semibold hover:underline">View Wedding Gifting <span aria-hidden="true">&rarr;</span></a>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
                    <img src="https://placehold.co/400x300/D1C0A8/333333?text=Custom+Monogram&font=playfairdisplay" alt="Custom Monogram Rumaals" class="w-full h-56 object-cover">
                    <div class="p-6 flex flex-col flex-grow">
                        <h3 class="text-2xl font-serif font-semibold text-ryuva-deep-green mb-2">Custom Monogram</h3>
                        <p class="text-ryuva-charcoal text-sm mb-4 flex-grow">Personalize your rumaal with bespoke monograms and unique designs.</p>
                        <a href="#" class="mt-auto self-start text-ryuva-gold font-semibold hover:underline">Discover Monograms <span aria-hidden="true">&rarr;</span></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="why-ryuva" class="py-16 md:py-24 bg-white scroll-animate">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-4xl font-serif font-bold text-ryuva-deep-green mb-16">The Ryuva Difference</h2>
            <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-10">
                <div class="flex flex-col items-center p-6 rounded-lg hover:shadow-lg transition-shadow">
                    <div class="bg-ryuva-cream p-5 rounded-full mb-5 inline-block ring-2 ring-ryuva-gold/30">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-10 h-10 text-ryuva-gold">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 14.188V12.008L18.25 12zm-2.25 0h.008v.008h-.008V12z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-ryuva-deep-green mb-2">Premium Natural Fabrics</h3>
                    <p class="text-ryuva-charcoal text-sm leading-relaxed">Indulge in the luxurious feel of carefully selected silks, cottons, and linens, chosen for their quality and comfort.</p>
                </div>
                <div class="flex flex-col items-center p-6 rounded-lg hover:shadow-lg transition-shadow">
                    <div class="bg-ryuva-cream p-5 rounded-full mb-5 inline-block ring-2 ring-ryuva-gold/30">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-10 h-10 text-ryuva-gold">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.82.61l-4.725-2.885a.563.563 0 00-.652 0L5.59 19.18a.562.562 0 01-.82-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-ryuva-deep-green mb-2">Handcrafted by Artisans</h3>
                    <p class="text-ryuva-charcoal text-sm leading-relaxed">Each rumaal is a masterpiece, meticulously created by skilled Indian artisans, preserving traditional techniques.</p>
                </div>
                <div class="flex flex-col items-center p-6 rounded-lg hover:shadow-lg transition-shadow">
                     <div class="bg-ryuva-cream p-5 rounded-full mb-5 inline-block ring-2 ring-ryuva-gold/30">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-10 h-10 text-ryuva-gold">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 01-1.5 1.5H5.25a1.5 1.5 0 01-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1014.625 7.5H12V4.875z" />
                          <path stroke-linecap="round" stroke-linejoin="round" d="M18.75 11.25c0-2.278-1.684-4.125-3.75-4.125S11.25 8.972 11.25 11.25v8.25c0 .828.672 1.5 1.5 1.5h3c.828 0 1.5-.672 1.5-1.5v-8.25zM6.75 11.25c0-2.278-1.684-4.125-3.75-4.125S-.75 8.972-.75 11.25v8.25c0 .828.672 1.5 1.5 1.5h3c.828 0 1.5-.672 1.5-1.5v-8.25z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-ryuva-deep-green mb-2">Perfect for Gifting</h3>
                    <p class="text-ryuva-charcoal text-sm leading-relaxed">An unforgettable gift that speaks volumes of thoughtfulness and refined taste for any occasion.</p>
                </div>
                <div class="flex flex-col items-center p-6 rounded-lg hover:shadow-lg transition-shadow">
                    <div class="bg-ryuva-cream p-5 rounded-full mb-5 inline-block ring-2 ring-ryuva-gold/30">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-10 h-10 text-ryuva-gold">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M9.53 16.122a3 3 0 00-5.78 1.128 2.25 2.25 0 01-2.4 2.245 4.5 4.5 0 008.4-2.245c0-.399-.078-.78-.22-1.128zm0 0a15.998 15.998 0 003.388-1.62m-5.043-.025a15.994 15.994 0 011.622-3.395m3.42 3.42a15.995 15.995 0 004.764-4.648l3.876-5.814a1.151 1.151 0 00-1.597-1.597L14.146 6.32a15.996 15.996 0 00-4.649 4.763m3.42 3.42a6.776 6.776 0 00-3.42-3.42" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-ryuva-deep-green mb-2">Personalization Available</h3>
                    <p class="text-ryuva-charcoal text-sm leading-relaxed">Add a unique touch with custom monograms, motifs, or messages to create a truly personal item.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="testimonials" class="py-16 md:py-24 bg-ryuva-cream scroll-animate">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-serif font-bold text-ryuva-deep-green text-center mb-16">Voices of Ryuva</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-xl shadow-xl flex flex-col items-center text-center transform hover:scale-105 transition-transform duration-300">
                    <img src="https://placehold.co/100x100/2E4034/FFFFFF?text=Priya+S.&font=inter" alt="Priya S." class="w-24 h-24 rounded-full mb-5 border-4 border-ryuva-gold/50 object-cover">
                    <div class="flex mb-2">
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    </div>
                    <p class="text-ryuva-charcoal italic text-center mb-4 text-base leading-relaxed">"The quality is exquisite, and the design is simply stunning. I received so many compliments at the wedding! Ryuva rumaals are truly special."</p>
                    <p class="text-ryuva-deep-green font-semibold text-center text-lg">Priya S., Bangalore</p>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-xl flex flex-col items-center text-center transform hover:scale-105 transition-transform duration-300">
                    <img src="https://placehold.co/100x100/B08D57/FFFFFF?text=Rohan+M.&font=inter" alt="Rohan M." class="w-24 h-24 rounded-full mb-5 border-4 border-ryuva-gold/50 object-cover">
                    <div class="flex mb-2">
                         <span class="text-yellow-400">★★★★★</span> </div>
                    <p class="text-ryuva-charcoal italic text-center mb-4 text-base leading-relaxed">"I ordered a custom monogrammed rumaal as a gift. The craftsmanship was exceptional, and the recipient loved it. Highly recommend Ryuva!"</p>
                    <p class="text-ryuva-deep-green font-semibold text-center text-lg">Rohan M., Mumbai</p>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-xl flex flex-col items-center text-center transform hover:scale-105 transition-transform duration-300 md:col-span-2 lg:col-span-1">
                    <img src="https://placehold.co/100x100/36454F/FFFFFF?text=Aisha+K.&font=inter" alt="Aisha K." class="w-24 h-24 rounded-full mb-5 border-4 border-ryuva-gold/50 object-cover">
                     <div class="flex mb-2">
                         <span class="text-yellow-400">★★★★★</span>
                    </div>
                    <p class="text-ryuva-charcoal italic text-center mb-4 text-base leading-relaxed">"The Minimal Luxe collection is my favorite. So elegant and versatile. The fabric feels amazing. It's my go-to accessory for a touch of class."</p>
                    <p class="text-ryuva-deep-green font-semibold text-center text-lg">Aisha K., Delhi</p>
                </div>
            </div>
        </div>
    </section>

    <section id="instagram" class="py-16 md:py-24 bg-white scroll-animate">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-4xl font-serif font-bold text-ryuva-deep-green mb-4">Follow Our Journey #RyuvaStyle</h2>
            <p class="text-lg text-ryuva-charcoal mb-12">Inspiration on Instagram <a href="#" class="text-ryuva-gold hover:underline font-semibold">@RyuvaOfficial</a></p>
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-4 mb-12">
                <div class="aspect-square bg-ryuva-light-gray rounded-lg overflow-hidden group relative">
                    <img src="https://placehold.co/300x300/FDFBF5/333?text=Ryuva+Style+1" alt="Instagram Post 1" class="w-full h-full object-cover group-hover:opacity-75 transition-opacity">
                    <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="white" class="w-10 h-10"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" /></svg>
                    </div>
                </div>
                 <div class="aspect-square bg-ryuva-light-gray rounded-lg overflow-hidden group relative">
                    <img src="https://placehold.co/300x300/B08D57/FFF?text=Ryuva+Style+2" alt="Instagram Post 2" class="w-full h-full object-cover group-hover:opacity-75 transition-opacity">
                     <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="white" class="w-10 h-10"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" /></svg>
                    </div>
                </div>
                <div class="aspect-square bg-ryuva-light-gray rounded-lg overflow-hidden group relative">
                    <img src="https://placehold.co/300x300/2E4034/FFF?text=Ryuva+Style+3" alt="Instagram Post 3" class="w-full h-full object-cover group-hover:opacity-75 transition-opacity">
                     <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="white" class="w-10 h-10"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" /></svg>
                    </div>
                </div>
                <div class="aspect-square bg-ryuva-light-gray rounded-lg overflow-hidden group relative">
                    <img src="https://placehold.co/300x300/36454F/FFF?text=Ryuva+Style+4" alt="Instagram Post 4" class="w-full h-full object-cover group-hover:opacity-75 transition-opacity">
                     <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="white" class="w-10 h-10"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" /></svg>
                    </div>
                </div>
                <div class="aspect-square bg-ryuva-light-gray rounded-lg overflow-hidden group relative">
                    <img src="https://placehold.co/300x300/D1C0A8/333?text=Ryuva+Style+5" alt="Instagram Post 5" class="w-full h-full object-cover group-hover:opacity-75 transition-opacity">
                     <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="white" class="w-10 h-10"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" /></svg>
                    </div>
                </div>
                <div class="aspect-square bg-ryuva-light-gray rounded-lg overflow-hidden group relative">
                    <img src="https://placehold.co/300x300/A07855/FFF?text=Ryuva+Style+6" alt="Instagram Post 6" class="w-full h-full object-cover group-hover:opacity-75 transition-opacity">
                     <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="white" class="w-10 h-10"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" /></svg>
                    </div>
                </div>
            </div>
            <a href="#" class="bg-ryuva-deep-green text-white py-3.5 px-10 rounded-lg text-lg font-semibold hover:bg-opacity-90 transition-colors shadow-lg transform hover:scale-105">Follow us on Instagram</a>
        </div>
    </section>

    <section id="newsletter" class="py-16 md:py-24 bg-ryuva-deep-green text-white scroll-animate">
        <div class="container mx-auto px-6 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 text-ryuva-gold mx-auto mb-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
            </svg>
            <h2 class="text-4xl font-serif font-bold mb-3">Stay Connected</h2>
            <p class="text-xl mb-3 text-gray-200">Get <span class="text-ryuva-gold font-semibold">10% Off</span> Your First Rumaal</p>
            <p class="text-gray-300 mb-8 max-w-lg mx-auto leading-relaxed">Be the first to know about new collections, special promotions, and the world of Ryuva. Join our family.</p>
            <form class="max-w-md mx-auto sm:flex sm:gap-3">
                <label for="email-address" class="sr-only">Email address</label>
                <input type="email" name="email-address" id="email-address" autocomplete="email" required
                       class="w-full px-5 py-3.5 placeholder-gray-500 focus:ring-2 focus:ring-ryuva-gold focus:border-ryuva-gold border-gray-300 rounded-lg text-ryuva-charcoal mb-3 sm:mb-0 shadow-sm"
                       placeholder="Enter your email address">
                <button type="submit"
                        class="w-full sm:w-auto bg-ryuva-gold hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-ryuva-deep-green focus:ring-ryuva-gold text-white font-semibold py-3.5 px-8 border border-transparent rounded-lg shadow-md transition-all duration-300 transform hover:scale-105">
                    Claim My 10%
                </button>
            </form>
        </div>
    </section>

    <footer id="contact" class="bg-ryuva-charcoal text-ryuva-cream py-16">
    <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8 mb-10 text-center md:text-left">
            <div class="lg:col-span-2">
                <h3 class="text-3xl font-serif font-semibold text-white mb-4">Ryuva</h3>
                <p class="text-sm text-gray-400 mb-4 max-w-md mx-auto md:mx-0">
                    Where Culture Meets Class. Discover handcrafted designer rumaals that blend traditional artistry with modern sophistication, perfect for every individual and occasion.
                </p>
            </div>
            <div>
                <h4 class="text-lg font-semibold text-white mb-4 uppercase tracking-wider">Quick Links</h4>
                <ul class="space-y-2 text-sm">
                    <li><a href="#about" class="hover:text-ryuva-gold transition-colors">About Us</a></li>
                    <li><a href="#collections" class="hover:text-ryuva-gold transition-colors">Collections</a></li>
                    <li><a href="#why-ryuva" class="hover:text-ryuva-gold transition-colors">Why Ryuva?</a></li>
                    <li><a href="#" class="hover:text-ryuva-gold transition-colors">Shipping & Returns</a></li>
                    <li><a href="#" class="hover:text-ryuva-gold transition-colors">FAQs</a></li>
                    <li><a href="#contact" class="hover:text-ryuva-gold transition-colors">Contact Us</a></li>
                </ul>
            </div>
            <div>
                <h4 class="text-lg font-semibold text-white mb-4 uppercase tracking-wider">Connect</h4>
                <ul class="space-y-2 text-sm">
                    <li><a href="mailto:<EMAIL>" class="hover:text-ryuva-gold transition-colors"><EMAIL></a></li>
                    <li><a href="tel:+910000000000" class="hover:text-ryuva-gold transition-colors">+91 (*************</a></li>
                </ul>
                <div class="flex justify-center md:justify-start space-x-5 mt-6">
                    <a href="#" class="text-gray-400 hover:text-ryuva-gold transition-colors" aria-label="Instagram">
                        <!-- Instagram SVG -->
                         <!-- Instagram SVG -->
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="..."/></svg>
<!-- WhatsApp SVG -->
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="..."/></svg>

                    </a>
                    <a href="#" class="text-gray-400 hover:text-ryuva-gold transition-colors" aria-label="WhatsApp">
                        <!-- WhatsApp SVG -->
                    </a>
                </div>
            </div>
        </div>
        <div class="border-t border-gray-700 pt-6 text-center text-sm text-gray-500">
            <p>&copy; 2025 Ryuva Lifestyle & Fashion Pvt Ltd. All rights reserved.</p>
        </div>
    </div>
</footer>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/scrollreveal/4.0.7/scrollreveal.min.js"></script>
    <script>
          document.addEventListener('DOMContentLoaded', function() {
      const items = document.querySelectorAll('.scroll-animate');
      const observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate');
            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.1 });
      items.forEach(item => observer.observe(item));
    });
        ScrollReveal().reveal('.scroll-animate', {
            duration: 1000,
            distance: '50px',
            easing: 'ease-in-out',
            origin: 'bottom',
            interval: 100
        });
    </script>
</body>
</html>