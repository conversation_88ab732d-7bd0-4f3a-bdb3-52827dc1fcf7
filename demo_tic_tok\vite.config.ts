import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
   test: {
    globals: true,                  // use global test APIs like `test()`
    environment: 'jsdom',           // simulate DOM
    setupFiles: './src/setupTests.ts',
    coverage: {
      reporter: ['text', 'lcov'],
      exclude: [
        'node_modules/',
        'tests/',
        'src/main.tsx',
        'src/vite-env.d.ts',
        'src/assets/**',
        'src/utils/**',
        'src/setupTests.ts',
        'eslint.config.js',
        'vite.config.ts',
        '**/*.d.ts'
      ],
    },
  },
})