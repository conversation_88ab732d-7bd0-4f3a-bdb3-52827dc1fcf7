import Square from "./square";
import { useTicTacToe } from "./useTicTacToe";

function TicTokToeGame() {
  const [
    { squares, isXNext, status },
    { handleSquareClick, resetGame }
  ] = useTicTacToe();

  return (
    <>
      {status === 'X' || status === 'O' ? (
        <div>
          {`${status} Won The Game !`}
          <button 
            className="m-2 p-4 rounded-2 bg-green-900" 
            onClick={resetGame}
          >
            Play Again
          </button>
        </div>
      ) : status === 'FULL' ? (
        <>
          <h1>It's a Draw!</h1>
          <button 
            className="m-2 p-4 rounded-2 bg-green-900" 
            onClick={resetGame}
          >
            Play Again
          </button>
        </>
      ) : (
        <div className="gamecontainer" data-testid="gamecontainer">
          <div className="row" data-testid="row">
            <Square value={squares[0]} onClick={() => handleSquareClick(0)} />
            <Square value={squares[1]} onClick={() => handleSquareClick(1)} />
            <Square value={squares[2]} onClick={() => handleSquareClick(2)} />
          </div>
          <div className="row" data-testid="row">
            <Square value={squares[3]} onClick={() => handleSquareClick(3)} />
            <Square value={squares[4]} onClick={() => handleSquareClick(4)} />
            <Square value={squares[5]} onClick={() => handleSquareClick(5)} />
          </div>
          <div className="row" data-testid="row">
            <Square value={squares[6]} onClick={() => handleSquareClick(6)} />
            <Square value={squares[7]} onClick={() => handleSquareClick(7)} />
            <Square value={squares[8]} onClick={() => handleSquareClick(8)} />
          </div>
        </div>
      )}
    </>
  );
}

export default TicTokToeGame;