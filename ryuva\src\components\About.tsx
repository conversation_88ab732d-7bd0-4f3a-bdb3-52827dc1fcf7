import React from 'react'

const About: React.FC = () => {
  return (
    <section id="about" className="py-16 md:py-24 bg-white scroll-animate">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div className="rounded-lg overflow-hidden shadow-2xl aspect-w-4 aspect-h-3">
            <img 
              src="https://placehold.co/600x450/FDFBF5/2E4034?text=Craftsmanship+%26+Modernity&font=playfairdisplay" 
              alt="Handcrafting a Ryuva rumaal with modern elegance" 
              className="w-full h-full object-cover"
            />
          </div>
          
          <div className="prose prose-lg max-w-none text-ryuva-charcoal">
            <h2 className="text-4xl font-serif font-bold text-ryuva-deep-green mb-6">
              The Essence of Ryuva
            </h2>
            <p className="leading-relaxed mb-6">
              <PERSON><PERSON><PERSON> was born from a desire to bridge generations – to take the timeless elegance of the traditional rumaal and reimagine it for today's discerning individual. We saw beauty in the intricate artistry passed down through families and the vibrant energy of modern style.
            </p>
            <p className="leading-relaxed mb-8">
              Each Ryuva piece is more than an accessory; it's a conversation starter, a piece of heritage handcrafted with meticulous care. We blend age-old cultural motifs with contemporary designs, ensuring every rumaal carries a personal touch and tells a unique story of sophistication.
            </p>
            
            <div className="space-y-6">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-8 h-8 text-ryuva-gold mr-4 flex-shrink-0 mt-1">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 14.188V12.008L18.25 12zm-2.25 0h.008v.008h-.008V12zm.75 2.25l.813 2.846H15L14.187 14.25l-2.846.813a4.5 4.5 0 00-3.09-3.09L5.25 9l2.846-.813a4.5 4.5 0 003.09-3.09L12 1.25l.813 2.846a4.5 4.5 0 003.09 3.09l2.846.813L14.25 9l.813 2.846a4.5 4.5 0 003.09 3.09L21.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
                </svg>
                <div>
                  <h3 className="font-semibold text-ryuva-deep-green text-xl">Handcrafted Detail</h3>
                  <p>Every stitch, every fold, a testament to artisanal skill.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-8 h-8 text-ryuva-gold mr-4 flex-shrink-0 mt-1">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />
                </svg>
                <div>
                  <h3 className="font-semibold text-ryuva-deep-green text-xl">Cultural Motifs</h3>
                  <p>Inspired by rich traditions, designed for modern sensibilities.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-8 h-8 text-ryuva-gold mr-4 flex-shrink-0 mt-1">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                </svg>
                <div>
                  <h3 className="font-semibold text-ryuva-deep-green text-xl">Personal Touch</h3>
                  <p>From monograms to bespoke designs, make it uniquely yours.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
