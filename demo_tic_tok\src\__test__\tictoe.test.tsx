import { render, screen, fireEvent } from '@testing-library/react'
import TicTokToeGame from '../components/tictoe'
import { useTicTacToe } from '../components/useTicTacToe'
import { vi } from 'vitest'

// Mock the custom hook
vi.mock('../components/useTicTacToe')

describe('TicTokToeGame', () => {
  const mockHandleSquareClick = vi.fn()
  const mockResetGame = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('renders empty game board', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { squares: Array(9).fill(null), isXNext: true, status: false },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ])

    const { container } = render(<TicTokToeGame />)
    
    const gameContainer = container.querySelector('[data-testid="gamecontainer"]')
    expect(gameContainer).toBeInTheDocument()
    
    const squares = container.querySelectorAll('[data-testid="square"]')
    expect(squares.length).toBe(9)
  })

  test('handles square click', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { squares: Array(9).fill(null), isXNext: true, status: false },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ])

    const { container } = render(<TicTokToeGame />)
    
    const squares = container.querySelectorAll('[data-testid="square"]')
    fireEvent.click(squares[0])
    
    expect(mockHandleSquareClick).toHaveBeenCalledWith(0)
  })

  test('shows X winner message and reset button works', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { squares: ['X', 'X', 'X', null, null, null, null, null, null], isXNext: false, status: 'X' },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ])

    render(<TicTokToeGame />)
    
    expect(screen.getByText(/X Won The Game/i)).toBeInTheDocument()
    
    const resetButton = screen.getByText(/Play Again/i)
    fireEvent.click(resetButton)
    
    expect(mockResetGame).toHaveBeenCalled()
  })

  test('shows O winner message', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { squares: [null, null, null, 'O', 'O', 'O', null, null, null], isXNext: true, status: 'O' },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ])

    render(<TicTokToeGame />)
    
    expect(screen.getByText(/O Won The Game/i)).toBeInTheDocument()
  })

  test('shows draw message and reset button works', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { 
        squares: ['X', 'O', 'X', 'X', 'O', 'X', 'O', 'X', 'O'], 
        isXNext: true, 
        status: 'FULL' 
      },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ])

    render(<TicTokToeGame />)
    
    expect(screen.getByText(/It's a Draw/i)).toBeInTheDocument()
    
    const resetButton = screen.getByText(/Play Again/i)
    fireEvent.click(resetButton)
    
    expect(mockResetGame).toHaveBeenCalled()
  })

  test('clicking all squares calls handleSquareClick with correct indices', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { squares: Array(9).fill(null), isXNext: true, status: false },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ])

    const { container } = render(<TicTokToeGame />)
    
    const squares = container.querySelectorAll('[data-testid="square"]')
    
    // Click all squares and verify correct index is passed
    for (let i = 0; i < squares.length; i++) {
      fireEvent.click(squares[i])
      expect(mockHandleSquareClick).toHaveBeenNthCalledWith(i + 1, i)
    }
  })
})