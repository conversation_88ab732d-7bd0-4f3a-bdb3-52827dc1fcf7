# Frontend Integration Guide for Ryuva API

## Overview
This guide explains how to integrate the Ryuva React frontend with the backend API for a seamless shopping experience.

## API Configuration

### 1. Create API Service
Create a service file to handle all API calls:

```typescript
// src/services/api.ts
const API_BASE_URL = 'http://localhost:3000/api';

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('authToken');
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }

    return data;
  }

  // Auth methods
  async login(email: string, password: string) {
    const data = await this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
    
    this.token = data.data.token;
    localStorage.setItem('authToken', this.token!);
    return data;
  }

  async getCurrentUser() {
    return this.request('/auth/me');
  }

  // Product methods
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
  }) {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    return this.request(`/products${queryString ? `?${queryString}` : ''}`);
  }

  async getProduct(id: number) {
    return this.request(`/products/${id}`);
  }

  async getFeaturedProducts() {
    return this.request('/products/featured');
  }

  // Customization methods
  async getCustomizationOptions(productId: number) {
    return this.request(`/customization/${productId}`);
  }

  async calculateCustomizationPrice(productId: number, customizations: any[]) {
    return this.request(`/customization/${productId}/calculate`, {
      method: 'POST',
      body: JSON.stringify({ customizations }),
    });
  }

  // Cart methods
  async getCart() {
    return this.request('/cart');
  }

  async addToCart(productId: number, quantity: number, customization?: any) {
    return this.request('/cart/add', {
      method: 'POST',
      body: JSON.stringify({ productId, quantity, customization }),
    });
  }

  async removeFromCart(itemId: number) {
    return this.request(`/cart/item/${itemId}`, {
      method: 'DELETE',
    });
  }

  // Wishlist methods
  async getWishlist() {
    return this.request('/wishlist');
  }

  async addToWishlist(productId: number) {
    return this.request(`/wishlist/${productId}`, {
      method: 'POST',
    });
  }

  async removeFromWishlist(productId: number) {
    return this.request(`/wishlist/${productId}`, {
      method: 'DELETE',
    });
  }

  // Order methods
  async createOrder(orderData: any) {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async getOrders() {
    return this.request('/orders');
  }

  async getOrder(id: number) {
    return this.request(`/orders/${id}`);
  }
}

export const apiService = new ApiService();
```

### 2. React Context for State Management

```typescript
// src/context/AppContext.tsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { apiService } from '../services/api';

interface AppState {
  user: any;
  cart: any;
  wishlist: any[];
  isAuthenticated: boolean;
  loading: boolean;
}

const initialState: AppState = {
  user: null,
  cart: null,
  wishlist: [],
  isAuthenticated: false,
  loading: true,
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<any>;
  actions: any;
}>({
  state: initialState,
  dispatch: () => {},
  actions: {},
});

function appReducer(state: AppState, action: any) {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload, isAuthenticated: !!action.payload };
    case 'SET_CART':
      return { ...state, cart: action.payload };
    case 'SET_WISHLIST':
      return { ...state, wishlist: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  const actions = {
    async login(email: string, password: string) {
      try {
        const response = await apiService.login(email, password);
        dispatch({ type: 'SET_USER', payload: response.data.user });
        return response;
      } catch (error) {
        throw error;
      }
    },

    async loadUserData() {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        const [userResponse, cartResponse, wishlistResponse] = await Promise.all([
          apiService.getCurrentUser(),
          apiService.getCart(),
          apiService.getWishlist(),
        ]);
        
        dispatch({ type: 'SET_USER', payload: userResponse.data.user });
        dispatch({ type: 'SET_CART', payload: cartResponse.data.cart });
        dispatch({ type: 'SET_WISHLIST', payload: wishlistResponse.data.wishlist });
      } catch (error) {
        console.error('Failed to load user data:', error);
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    async addToCart(productId: number, quantity: number, customization?: any) {
      try {
        await apiService.addToCart(productId, quantity, customization);
        const cartResponse = await apiService.getCart();
        dispatch({ type: 'SET_CART', payload: cartResponse.data.cart });
      } catch (error) {
        throw error;
      }
    },

    async addToWishlist(productId: number) {
      try {
        await apiService.addToWishlist(productId);
        const wishlistResponse = await apiService.getWishlist();
        dispatch({ type: 'SET_WISHLIST', payload: wishlistResponse.data.wishlist });
      } catch (error) {
        throw error;
      }
    },
  };

  useEffect(() => {
    const token = localStorage.getItem('authToken');
    if (token) {
      actions.loadUserData();
    } else {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  return (
    <AppContext.Provider value={{ state, dispatch, actions }}>
      {children}
    </AppContext.Provider>
  );
}

export const useApp = () => useContext(AppContext);
```

### 3. Product Integration Example

```typescript
// src/components/ProductCard.tsx
import React from 'react';
import { useApp } from '../context/AppContext';

interface ProductCardProps {
  product: any;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const { state, actions } = useApp();

  const handleAddToCart = async () => {
    try {
      await actions.addToCart(product.id, 1);
      // Show success message
    } catch (error) {
      // Show error message
    }
  };

  const handleAddToWishlist = async () => {
    try {
      await actions.addToWishlist(product.id);
      // Show success message
    } catch (error) {
      // Show error message
    }
  };

  return (
    <div className="product-card">
      <img src={product.images[0]?.url} alt={product.name} />
      <h3>{product.name}</h3>
      <p>₹{product.price}</p>
      {product.comparePrice && (
        <span className="original-price">₹{product.comparePrice}</span>
      )}
      <div className="actions">
        <button onClick={handleAddToCart}>Add to Cart</button>
        <button onClick={handleAddToWishlist}>♡</button>
      </div>
    </div>
  );
};
```

### 4. Customization Integration

```typescript
// src/components/CustomizationForm.tsx
import React, { useState, useEffect } from 'react';
import { apiService } from '../services/api';

interface CustomizationFormProps {
  productId: number;
  onCustomizationChange: (customization: any, price: number) => void;
}

export const CustomizationForm: React.FC<CustomizationFormProps> = ({
  productId,
  onCustomizationChange,
}) => {
  const [options, setOptions] = useState<any>(null);
  const [customization, setCustomization] = useState({
    type: 'monogram',
    details: {
      text: '',
      font: '',
      color: '',
      position: '',
    },
  });

  useEffect(() => {
    loadCustomizationOptions();
  }, [productId]);

  const loadCustomizationOptions = async () => {
    try {
      const response = await apiService.getCustomizationOptions(productId);
      setOptions(response.data);
    } catch (error) {
      console.error('Failed to load customization options:', error);
    }
  };

  const calculatePrice = async () => {
    try {
      const response = await apiService.calculateCustomizationPrice(productId, [customization]);
      onCustomizationChange(customization, response.data.customizationPrice);
    } catch (error) {
      console.error('Failed to calculate price:', error);
    }
  };

  useEffect(() => {
    if (customization.details.text && customization.details.font) {
      calculatePrice();
    }
  }, [customization]);

  if (!options) return <div>Loading customization options...</div>;

  return (
    <div className="customization-form">
      <h3>Customize Your Rumaal</h3>
      
      {options.customizationOptions.monogram?.enabled && (
        <div className="monogram-section">
          <h4>Monogram</h4>
          
          <input
            type="text"
            placeholder="Enter text (max 10 chars)"
            maxLength={10}
            value={customization.details.text}
            onChange={(e) =>
              setCustomization({
                ...customization,
                details: { ...customization.details, text: e.target.value },
              })
            }
          />
          
          <select
            value={customization.details.font}
            onChange={(e) =>
              setCustomization({
                ...customization,
                details: { ...customization.details, font: e.target.value },
              })
            }
          >
            <option value="">Select Font</option>
            {options.customizationOptions.monogram.fonts.map((font: string) => (
              <option key={font} value={font}>
                {font}
              </option>
            ))}
          </select>
          
          <select
            value={customization.details.color}
            onChange={(e) =>
              setCustomization({
                ...customization,
                details: { ...customization.details, color: e.target.value },
              })
            }
          >
            <option value="">Select Color</option>
            {options.customizationOptions.monogram.colors.map((color: string) => (
              <option key={color} value={color}>
                {color}
              </option>
            ))}
          </select>
        </div>
      )}
    </div>
  );
};
```

## Integration Steps

1. **Install the API service** in your React app
2. **Wrap your app** with the AppProvider
3. **Update existing components** to use the API service
4. **Add authentication** to protected routes
5. **Implement error handling** for API calls
6. **Add loading states** for better UX

## Testing the Integration

1. Start the backend server: `node simple-server.js`
2. Start the React app: `npm run dev`
3. Test login with: `<EMAIL>` / `password123`
4. Test product browsing, cart, and wishlist functionality

This integration provides a complete e-commerce experience with real-time data synchronization between frontend and backend.
