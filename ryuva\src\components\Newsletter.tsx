import React, { useState } from 'react'

const Newsletter: React.FC = () => {
  const [email, setEmail] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle newsletter subscription
    console.log('Newsletter subscription:', email)
    // Reset form
    setEmail('')
  }

  return (
    <section id="newsletter" className="py-16 md:py-24 bg-ryuva-deep-green text-white scroll-animate">
      <div className="container mx-auto px-6 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-16 h-16 text-ryuva-gold mx-auto mb-6">
          <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
        </svg>
        
        <h2 className="text-4xl font-serif font-bold mb-3">Stay Connected</h2>
        <p className="text-xl mb-3 text-gray-200">
          Get <span className="text-ryuva-gold font-semibold">10% Off</span> Your First Rumaal
        </p>
        <p className="text-gray-300 mb-8 max-w-lg mx-auto leading-relaxed">
          Be the first to know about new collections, special promotions, and the world of Ryuva. Join our family.
        </p>
        
        <form onSubmit={handleSubmit} className="max-w-md mx-auto sm:flex sm:gap-3">
          <label htmlFor="email-address" className="sr-only">Email address</label>
          <input 
            type="email" 
            name="email-address" 
            id="email-address" 
            autoComplete="email" 
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-5 py-3.5 placeholder-gray-500 focus:ring-2 focus:ring-ryuva-gold focus:border-ryuva-gold border-gray-300 rounded-lg text-ryuva-charcoal mb-3 sm:mb-0 shadow-sm"
            placeholder="Enter your email address"
          />
          <button 
            type="submit"
            className="w-full sm:w-auto bg-ryuva-gold hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-ryuva-deep-green focus:ring-ryuva-gold text-white font-semibold py-3.5 px-8 border border-transparent rounded-lg shadow-md transition-all duration-300 transform hover:scale-105"
          >
            Claim My 10%
          </button>
        </form>
      </div>
    </section>
  )
}

export default Newsletter
