import Order from '../models/Order.js';
import Cart from '../models/Cart.js';
import Product from '../models/Product.js';
import { asyncHandler } from '../middleware/errorHandler.js';

// @desc    Create new order
// @route   POST /api/orders
// @access  Private
export const createOrder = asyncHandler(async (req, res) => {
  const {
    items,
    shippingAddress,
    billingAddress,
    paymentMethod,
    couponCode
  } = req.body;

  if (!items || items.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'No order items provided'
    });
  }

  // Validate products and calculate pricing
  let subtotal = 0;
  let customizationTotal = 0;
  const orderItems = [];

  for (const item of items) {
    const product = await Product.findById(item.productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: `Product not found: ${item.productId}`
      });
    }

    // Check stock availability
    if (product.inventory.trackQuantity && product.inventory.quantity < item.quantity) {
      return res.status(400).json({
        success: false,
        message: `Insufficient stock for ${product.name}`
      });
    }

    const itemSubtotal = product.price * item.quantity;
    const itemCustomizationCost = (item.customization?.additionalPrice || 0) * item.quantity;

    subtotal += itemSubtotal;
    customizationTotal += itemCustomizationCost;

    orderItems.push({
      product: product._id,
      quantity: item.quantity,
      price: product.price,
      customization: item.customization || { type: 'none', additionalPrice: 0 },
      subtotal: itemSubtotal + itemCustomizationCost
    });

    // Update inventory
    if (product.inventory.trackQuantity) {
      product.inventory.quantity -= item.quantity;
      await product.save();
    }
  }

  // Calculate shipping and tax
  const shippingCost = subtotal >= 500 ? 0 : 50; // Free shipping over ₹500
  const tax = Math.round((subtotal + customizationTotal) * 0.18); // 18% GST
  
  // Apply coupon discount if provided
  let discount = 0;
  if (couponCode) {
    // Simple coupon logic - in production, this would check a coupons database
    if (couponCode === 'WELCOME10') {
      discount = Math.round((subtotal + customizationTotal) * 0.1);
    } else if (couponCode === 'RYUVA20') {
      discount = Math.round((subtotal + customizationTotal) * 0.2);
    }
  }

  const total = subtotal + customizationTotal + shippingCost + tax - discount;

  // Create order
  const order = await Order.create({
    user: req.user.id,
    items: orderItems,
    shippingAddress,
    billingAddress: billingAddress || shippingAddress,
    pricing: {
      subtotal,
      customizationTotal,
      shippingCost,
      tax,
      discount,
      total
    },
    payment: {
      method: paymentMethod,
      status: 'pending'
    },
    coupon: couponCode ? {
      code: couponCode,
      discountType: 'percentage',
      discountValue: discount
    } : undefined,
    timeline: [{
      status: 'pending',
      note: 'Order created'
    }]
  });

  // Clear user's cart
  const cart = await Cart.findOne({ user: req.user.id });
  if (cart) {
    await cart.clearCart();
  }

  res.status(201).json({
    success: true,
    message: 'Order created successfully',
    data: { order }
  });
});

// @desc    Get user orders
// @route   GET /api/orders
// @access  Private
export const getUserOrders = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const orders = await Order.find({ user: req.user.id })
    .populate('items.product', 'name images sku')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const total = await Order.countDocuments({ user: req.user.id });

  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get single order
// @route   GET /api/orders/:id
// @access  Private
export const getOrder = asyncHandler(async (req, res) => {
  const order = await Order.findOne({
    _id: req.params.id,
    user: req.user.id
  }).populate('items.product');

  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  res.json({
    success: true,
    data: { order }
  });
});

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private
export const cancelOrder = asyncHandler(async (req, res) => {
  const order = await Order.findOne({
    _id: req.params.id,
    user: req.user.id
  });

  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  if (!['pending', 'confirmed'].includes(order.status)) {
    return res.status(400).json({
      success: false,
      message: 'Order cannot be cancelled at this stage'
    });
  }

  // Restore inventory
  for (const item of order.items) {
    const product = await Product.findById(item.product);
    if (product && product.inventory.trackQuantity) {
      product.inventory.quantity += item.quantity;
      await product.save();
    }
  }

  order.status = 'cancelled';
  order.timeline.push({
    status: 'cancelled',
    note: 'Order cancelled by customer'
  });

  await order.save();

  res.json({
    success: true,
    message: 'Order cancelled successfully',
    data: { order }
  });
});

// @desc    Get all orders (Admin only)
// @route   GET /api/admin/orders
// @access  Private/Admin
export const getAllOrders = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;
  const status = req.query.status;

  let query = {};
  if (status) {
    query.status = status;
  }

  const orders = await Order.find(query)
    .populate('user', 'firstName lastName email')
    .populate('items.product', 'name sku')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const total = await Order.countDocuments(query);

  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Update order status (Admin only)
// @route   PUT /api/admin/orders/:id/status
// @access  Private/Admin
export const updateOrderStatus = asyncHandler(async (req, res) => {
  const { status, note, trackingNumber, carrier } = req.body;

  const order = await Order.findById(req.params.id);
  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  order.status = status;
  order.timeline.push({
    status,
    note: note || `Order status updated to ${status}`,
    updatedBy: req.user.id
  });

  if (status === 'shipped' && trackingNumber) {
    order.tracking = {
      carrier: carrier || 'Standard',
      trackingNumber,
      shippedAt: new Date()
    };
  }

  if (status === 'delivered') {
    order.tracking.deliveredAt = new Date();
  }

  await order.save();

  res.json({
    success: true,
    message: 'Order status updated successfully',
    data: { order }
  });
});
