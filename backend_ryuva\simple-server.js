import express from 'express';

const app = express();
const PORT = 3000;

// Middleware to parse JSON
app.use(express.json());

// CORS middleware (simple version)
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Sample products data
const sampleProducts = [
  {
    id: 1,
    name: "Heritage Paisley Rumaal",
    description: "Exquisite handcrafted rumaal featuring traditional paisley motifs in rich burgundy and gold. Each piece tells a story of generations of artisanal expertise, perfect for special occasions and gifting.",
    shortDescription: "Traditional paisley design in burgundy and gold",
    sku: "RYU-HER-001",
    category: "heritage",
    subcategory: "unisex",
    price: 899,
    comparePrice: 1199,
    images: [
      {
        url: "https://placehold.co/800x800/8B0000/FFD700?text=Heritage+Paisley+Rumaal",
        alt: "Heritage Paisley Rumaal - Front View",
        isPrimary: true
      }
    ],
    fabric: {
      type: "silk",
      care: ["Dry clean only", "Store flat", "Avoid direct sunlight"],
      origin: "Varanasi, India"
    },
    colors: [
      { name: "Burgundy Gold", hex: "#8B0000" }
    ],
    patterns: ["Paisley", "Traditional", "Floral"],
    tags: ["heritage", "silk", "handcrafted", "traditional", "paisley"],
    inventory: {
      quantity: 25,
      stockStatus: "in-stock"
    },
    features: ["Hand-embroidered paisley motifs", "Pure silk fabric", "Traditional craftsmanship", "Gift-ready packaging"],
    isCustomizable: true,
    customizationOptions: {
      monogram: {
        enabled: true,
        price: 150,
        positions: ["bottom-right", "center"],
        fonts: ["Script", "Block"],
        colors: ["Gold", "Silver", "White"]
      }
    },
    ratings: {
      average: 4.8,
      count: 24
    },
    isFeatured: true,
    discountPercentage: 25
  },
  {
    id: 2,
    name: "Minimal Luxe Cotton",
    description: "Clean, sophisticated design meets premium organic cotton. This minimalist rumaal features subtle texture and impeccable finishing, perfect for the modern professional.",
    shortDescription: "Minimalist design in premium organic cotton",
    sku: "RYU-MIN-001",
    category: "minimal-luxe",
    subcategory: "unisex",
    price: 599,
    comparePrice: 799,
    images: [
      {
        url: "https://placehold.co/800x800/F5F5F5/333333?text=Minimal+Luxe+Cotton",
        alt: "Minimal Luxe Cotton Rumaal",
        isPrimary: true
      }
    ],
    fabric: {
      type: "organic-cotton",
      care: ["Machine wash cold", "Tumble dry low", "Iron on medium heat"],
      origin: "Gujarat, India"
    },
    colors: [
      { name: "Pure White", hex: "#FFFFFF" },
      { name: "Soft Grey", hex: "#F5F5F5" }
    ],
    patterns: ["Solid", "Minimal"],
    tags: ["minimal", "organic-cotton", "modern", "professional"],
    inventory: {
      quantity: 50,
      stockStatus: "in-stock"
    },
    features: ["100% organic cotton", "Minimalist design", "Professional finish", "Eco-friendly"],
    isCustomizable: true,
    customizationOptions: {
      monogram: {
        enabled: true,
        price: 100,
        positions: ["bottom-right", "center"],
        fonts: ["Modern", "Classic"],
        colors: ["Navy", "Black", "Grey"]
      }
    },
    ratings: {
      average: 4.6,
      count: 18
    },
    isFeatured: true,
    discountPercentage: 25
  },
  {
    id: 3,
    name: "Wedding Elegance Set",
    description: "Luxurious set of 6 rumaals designed specifically for wedding ceremonies and gifting. Features intricate gold threadwork and comes in an elegant gift box.",
    shortDescription: "Elegant wedding set with gold threadwork",
    sku: "RYU-WED-001",
    category: "wedding-gifting",
    subcategory: "unisex",
    price: 2499,
    comparePrice: 3299,
    images: [
      {
        url: "https://placehold.co/800x800/FFD700/8B0000?text=Wedding+Elegance+Set",
        alt: "Wedding Elegance Set",
        isPrimary: true
      }
    ],
    fabric: {
      type: "silk",
      care: ["Dry clean only", "Store in provided box", "Handle with care"],
      origin: "Banaras, India"
    },
    colors: [
      { name: "Ivory Gold", hex: "#FFFFF0" }
    ],
    patterns: ["Traditional", "Gold Thread", "Wedding"],
    tags: ["wedding", "gift-set", "luxury", "gold-thread", "ceremonial"],
    inventory: {
      quantity: 15,
      stockStatus: "in-stock"
    },
    features: ["Set of 6 rumaals", "Gold threadwork", "Luxury gift box", "Wedding ceremony ready"],
    isCustomizable: true,
    customizationOptions: {
      monogram: {
        enabled: true,
        price: 300,
        positions: ["center", "bottom-right"],
        fonts: ["Elegant Script", "Traditional"],
        colors: ["Gold", "Silver"]
      }
    },
    ratings: {
      average: 4.9,
      count: 12
    },
    isFeatured: true,
    discountPercentage: 24
  },
  {
    id: 4,
    name: "Custom Monogram Classic",
    description: "Personalize your style with our custom monogram service. Choose from elegant fonts and premium fabrics to create a truly unique piece.",
    shortDescription: "Personalized monogram handkerchief",
    sku: "RYU-MON-001",
    category: "custom-monogram",
    subcategory: "unisex",
    price: 799,
    comparePrice: 999,
    images: [
      {
        url: "https://placehold.co/800x800/2E4034/FDFBF5?text=Custom+Monogram",
        alt: "Custom Monogram Rumaal",
        isPrimary: true
      }
    ],
    fabric: {
      type: "linen",
      care: ["Machine wash cold", "Air dry", "Iron while damp"],
      origin: "Kerala, India"
    },
    colors: [
      { name: "Classic White", hex: "#FFFFFF" },
      { name: "Cream", hex: "#FDFBF5" },
      { name: "Navy", hex: "#2E4034" }
    ],
    patterns: ["Monogram", "Custom"],
    tags: ["custom", "monogram", "personalized", "linen"],
    inventory: {
      quantity: 100,
      stockStatus: "in-stock"
    },
    features: ["Custom monogramming", "Premium linen", "Multiple font options", "Gift packaging"],
    isCustomizable: true,
    customizationOptions: {
      monogram: {
        enabled: true,
        price: 0,
        positions: ["center", "bottom-right", "top-left"],
        fonts: ["Script", "Block", "Cursive", "Modern"],
        colors: ["Gold", "Silver", "Navy", "Black", "White"]
      }
    },
    ratings: {
      average: 4.7,
      count: 35
    },
    isFeatured: false,
    discountPercentage: 20
  }
];

// API Routes
app.get('/api/products', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 12;
  const category = req.query.category;
  const search = req.query.search;
  
  let filteredProducts = [...sampleProducts];
  
  // Filter by category
  if (category) {
    filteredProducts = filteredProducts.filter(p => p.category === category);
  }
  
  // Filter by search
  if (search) {
    const searchLower = search.toLowerCase();
    filteredProducts = filteredProducts.filter(p => 
      p.name.toLowerCase().includes(searchLower) ||
      p.description.toLowerCase().includes(searchLower) ||
      p.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );
  }
  
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      products: paginatedProducts,
      pagination: {
        page,
        limit,
        total: filteredProducts.length,
        pages: Math.ceil(filteredProducts.length / limit)
      }
    }
  });
});

app.get('/api/products/featured', (req, res) => {
  const featuredProducts = sampleProducts.filter(p => p.isFeatured);
  
  res.json({
    success: true,
    data: { products: featuredProducts }
  });
});

app.get('/api/products/:id', (req, res) => {
  const product = sampleProducts.find(p => p.id === parseInt(req.params.id));
  
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }
  
  res.json({
    success: true,
    data: { product }
  });
});

app.get('/api/products/category/:category', (req, res) => {
  const categoryProducts = sampleProducts.filter(p => p.category === req.params.category);
  
  res.json({
    success: true,
    data: {
      products: categoryProducts,
      pagination: {
        page: 1,
        limit: categoryProducts.length,
        total: categoryProducts.length,
        pages: 1
      }
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: 'development',
    version: '1.0.0'
  });
});

// API documentation endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'Ryuva E-commerce API (Simple Mode)',
    version: '1.0.0',
    endpoints: {
      products: '/api/products',
      featured: '/api/products/featured',
      categories: '/api/products/category/:category',
      singleProduct: '/api/products/:id'
    },
    note: 'This is a simple server with sample data for development.'
  });
});

// Error handling middleware
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`
  });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 Ryuva Simple API Server running on port ${PORT}`);
  console.log(`📱 Frontend URL: http://localhost:5173`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log(`💡 Health Check: http://localhost:${PORT}/health`);
  console.log(`📚 API Docs: http://localhost:${PORT}/api`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.log(`Error: ${err.message}`);
  server.close(() => {
    process.exit(1);
  });
});
