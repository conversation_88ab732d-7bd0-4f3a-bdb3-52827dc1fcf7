import React, { useState } from 'react'

const Header: React.FC = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false)

  const handleIsOpen = () => {
    setIsOpen(!isOpen)
  }

  return (
    <nav className="bg-white/90 backdrop-blur-lg shadow-md fixed w-full z-50 top-0 transition-all duration-300">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          <div className="flex-shrink-0">
            <a href="#home" className="text-4xl font-serif font-bold text-ryuva-deep-green hover:text-ryuva-gold transition-colors">
              Ryuva
            </a>
          </div>

          <div className="hidden md:flex items-center space-x-6">
            <a href="#about" className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">
              About
            </a>
            <a href="#collections" className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">
              Collections
            </a>
            <a href="#why-ryuva" className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">
              Why Ryuva?
            </a>
            <a href="#contact" className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">
              Contact
            </a>
            <a href="#shop" className="ml-4 bg-ryuva-gold text-white py-2.5 px-6 rounded-lg hover:bg-opacity-80 transition-all duration-300 ease-in-out shadow-sm font-semibold text-sm">
              Shop Now
            </a>
          </div>

          <div className="md:hidden">
            <button
              id="mobile-menu-button"
              type="button"
              onClick={handleIsOpen}
              className="inline-flex items-center justify-center p-2 rounded-md text-ryuva-charcoal hover:text-ryuva-gold hover:bg-ryuva-light-gray focus:outline-none focus:ring-2 focus:ring-inset focus:ring-ryuva-gold"
              aria-controls="mobile-menu"
              aria-expanded={isOpen}
            >
              <span className="sr-only">Open main menu</span>
              {!isOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="md:hidden" id="mobile-menu">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <a href="#about" className="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">
              About
            </a>
            <a href="#collections" className="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">
              Collections
            </a>
            <a href="#why-ryuva" className="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">
              Why Ryuva?
            </a>
            <a href="#contact" className="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">
              Contact
            </a>
            <a href="#shop" className="bg-ryuva-gold text-white block w-full text-center mt-2 py-2.5 px-6 rounded-lg hover:bg-opacity-80 transition-all duration-300 ease-in-out shadow-sm font-semibold text-sm">
              Shop Now
            </a>
          </div>
        </div>
      )}
    </nav>
  )
}

export default Header