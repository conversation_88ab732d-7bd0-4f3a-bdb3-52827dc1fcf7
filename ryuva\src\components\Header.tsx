import React,{useState} from 'react'
import {Link} from 'react-router-dom'


const Header:React.FC = () => {
  const [isOpen,setIsOpen] = useState<boolean>(false)
  const handleIsOpen=()=>{
    setIsOpen(!isOpen)
  }
  
  return <>
  <div className='bg-gray-300 shadow-md '>
    <div className='container mx-auto px-4'>
      <div className='flex justify-between h-20' >
        <div className='flex-shrink-0'><a href="#Home" className="text-4xl font-serif font-bold text-[var(--color-ryuva-deep-green)] hover:text-[var(--color-ryuva-gold)] transition-colors">Ryuva</a></div>
        <div className='md:hidden'>
        {isOpen===true?<button onClick={handleIsOpen}>X</button>:<button onClick={handleIsOpen}>|=|</button>}
        </div>
        {isOpen && <div className='z-1 '></div>}
       
        <nav className='hidden md:flex justify-between gap-4'>
          <Link to="/">
            Home
          </Link>
          <Link to="/about">
          About
          </Link>
          <a href="#Collections">Collections</a>
          <Link to="/contacts">
          Contacts
          </Link>
          <Link to="/shop">
            <button>Shop now</button>
          </Link>
        </nav>
      </div>
    </div>
  </div>
  </>
}

export default Header