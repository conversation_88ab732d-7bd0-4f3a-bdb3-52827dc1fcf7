import React from 'react'

const Testimonials: React.FC = () => {
  return (
    <section id="testimonials" className="py-16 md:py-24 bg-ryuva-cream scroll-animate">
      <div className="container mx-auto px-6">
        <h2 className="text-4xl font-serif font-bold text-ryuva-deep-green text-center mb-16">
          Voices of Ryuva
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-white p-8 rounded-xl shadow-xl flex flex-col items-center text-center transform hover:scale-105 transition-transform duration-300">
            <img 
              src="https://placehold.co/100x100/2E4034/FFFFFF?text=Priya+S.&font=inter" 
              alt="Priya S." 
              className="w-24 h-24 rounded-full mb-5 border-4 border-ryuva-gold/50 object-cover"
            />
            <div className="flex mb-2">
              {[...Array(5)].map((_, i) => (
                <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
              ))}
            </div>
            <p className="text-ryuva-charcoal italic text-center mb-4 text-base leading-relaxed">
              "The quality is exquisite, and the design is simply stunning. I received so many compliments at the wedding! Ryuva rumaals are truly special."
            </p>
            <p className="text-ryuva-deep-green font-semibold text-center text-lg">
              Priya S., Bangalore
            </p>
          </div>
          
          <div className="bg-white p-8 rounded-xl shadow-xl flex flex-col items-center text-center transform hover:scale-105 transition-transform duration-300">
            <img 
              src="https://placehold.co/100x100/B08D57/FFFFFF?text=Rohan+M.&font=inter" 
              alt="Rohan M." 
              className="w-24 h-24 rounded-full mb-5 border-4 border-ryuva-gold/50 object-cover"
            />
            <div className="flex mb-2">
              <span className="text-yellow-400">★★★★★</span>
            </div>
            <p className="text-ryuva-charcoal italic text-center mb-4 text-base leading-relaxed">
              "I ordered a custom monogrammed rumaal as a gift. The craftsmanship was exceptional, and the recipient loved it. Highly recommend Ryuva!"
            </p>
            <p className="text-ryuva-deep-green font-semibold text-center text-lg">
              Rohan M., Mumbai
            </p>
          </div>
          
          <div className="bg-white p-8 rounded-xl shadow-xl flex flex-col items-center text-center transform hover:scale-105 transition-transform duration-300 md:col-span-2 lg:col-span-1">
            <img 
              src="https://placehold.co/100x100/36454F/FFFFFF?text=Aisha+K.&font=inter" 
              alt="Aisha K." 
              className="w-24 h-24 rounded-full mb-5 border-4 border-ryuva-gold/50 object-cover"
            />
            <div className="flex mb-2">
              <span className="text-yellow-400">★★★★★</span>
            </div>
            <p className="text-ryuva-charcoal italic text-center mb-4 text-base leading-relaxed">
              "The Minimal Luxe collection is my favorite. So elegant and versatile. The fabric feels amazing. It's my go-to accessory for a touch of class."
            </p>
            <p className="text-ryuva-deep-green font-semibold text-center text-lg">
              Aisha K., Delhi
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Testimonials
