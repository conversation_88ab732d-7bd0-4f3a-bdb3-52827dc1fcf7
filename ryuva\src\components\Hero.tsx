import React from 'react'

const Hero: React.FC = () => {
  return (
    <header
      id="home"
      className="min-h-screen bg-cover bg-center flex items-center justify-center relative pt-20"
      style={{
        backgroundImage: "url('https://placehold.co/1920x1080/2E4034/FDFBF5?text=Ryuva%3A+Elegance+for+All&font=playfairdisplay')"
      }}
    >
      <div className="absolute inset-0 bg-gradient-to-t from-ryuva-deep-green/50 via-transparent to-transparent"></div>
      <div className="absolute inset-0 bg-black/30"></div>

      <div className="text-center z-10 p-6 max-w-3xl mx-auto">
        <h1 className="text-5xl sm:text-6xl md:text-7xl font-serif font-bold text-white mb-6 leading-tight shadow-text">
          Ryuva
        </h1>
        <p className="text-xl sm:text-2xl md:text-3xl font-serif text-gray-100 mb-10 shadow-text">
          "Where Culture Meets Class"
        </p>

        <div className="space-y-4 sm:space-y-0 sm:flex sm:flex-wrap sm:justify-center sm:gap-4">
          <a
            href="#shop"
            className="inline-block bg-ryuva-gold text-white py-3 px-8 rounded-lg text-lg font-semibold hover:bg-opacity-80 transition-all duration-300 ease-in-out transform hover:scale-105 shadow-xl"
          >
            Shop Now
          </a>
          <a
            href="#customize"
            className="inline-block border-2 border-ryuva-gold text-white py-3 px-8 rounded-lg text-lg font-semibold hover:bg-ryuva-gold hover:text-white transition-all duration-300 ease-in-out transform hover:scale-105 shadow-xl"
          >
            Customize Yours
          </a>
          <a
            href="#collections"
            className="inline-block border-2 border-gray-200 text-white py-3 px-8 rounded-lg text-lg font-semibold hover:bg-gray-200 hover:text-ryuva-charcoal transition-all duration-300 ease-in-out transform hover:scale-105 shadow-xl mt-4 sm:mt-0"
          >
            Explore Collection
          </a>
        </div>
      </div>
    </header>
  )
}

export default Hero