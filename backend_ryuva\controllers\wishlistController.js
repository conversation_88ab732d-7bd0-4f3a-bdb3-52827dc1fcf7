import User from '../models/User.js';
import Product from '../models/Product.js';
import { asyncHandler } from '../middleware/errorHandler.js';

// @desc    Get user wishlist
// @route   GET /api/wishlist
// @access  Private
export const getWishlist = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id)
    .populate({
      path: 'wishlist',
      match: { isActive: true },
      select: 'name price comparePrice images category ratings inventory.quantity'
    });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  res.json({
    success: true,
    data: {
      wishlist: user.wishlist,
      count: user.wishlist.length
    }
  });
});

// @desc    Add product to wishlist
// @route   POST /api/wishlist/:productId
// @access  Private
export const addToWishlist = asyncHandler(async (req, res) => {
  const { productId } = req.params;

  // Check if product exists and is active
  const product = await Product.findOne({ _id: productId, isActive: true });
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  const user = await User.findById(req.user.id);
  
  // Check if product is already in wishlist
  if (user.wishlist.includes(productId)) {
    return res.status(400).json({
      success: false,
      message: 'Product already in wishlist'
    });
  }

  // Add to wishlist
  user.wishlist.push(productId);
  await user.save();

  res.json({
    success: true,
    message: 'Product added to wishlist',
    data: {
      productId,
      wishlistCount: user.wishlist.length
    }
  });
});

// @desc    Remove product from wishlist
// @route   DELETE /api/wishlist/:productId
// @access  Private
export const removeFromWishlist = asyncHandler(async (req, res) => {
  const { productId } = req.params;

  const user = await User.findById(req.user.id);
  
  // Check if product is in wishlist
  if (!user.wishlist.includes(productId)) {
    return res.status(400).json({
      success: false,
      message: 'Product not in wishlist'
    });
  }

  // Remove from wishlist
  user.wishlist = user.wishlist.filter(id => id.toString() !== productId);
  await user.save();

  res.json({
    success: true,
    message: 'Product removed from wishlist',
    data: {
      productId,
      wishlistCount: user.wishlist.length
    }
  });
});

// @desc    Clear entire wishlist
// @route   DELETE /api/wishlist
// @access  Private
export const clearWishlist = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id);
  
  user.wishlist = [];
  await user.save();

  res.json({
    success: true,
    message: 'Wishlist cleared successfully',
    data: {
      wishlistCount: 0
    }
  });
});

// @desc    Check if product is in wishlist
// @route   GET /api/wishlist/check/:productId
// @access  Private
export const checkWishlistStatus = asyncHandler(async (req, res) => {
  const { productId } = req.params;

  const user = await User.findById(req.user.id);
  const isInWishlist = user.wishlist.includes(productId);

  res.json({
    success: true,
    data: {
      productId,
      isInWishlist,
      wishlistCount: user.wishlist.length
    }
  });
});

// @desc    Move wishlist item to cart
// @route   POST /api/wishlist/:productId/move-to-cart
// @access  Private
export const moveToCart = asyncHandler(async (req, res) => {
  const { productId } = req.params;
  const { quantity = 1, customization } = req.body;

  // Check if product exists and is active
  const product = await Product.findOne({ _id: productId, isActive: true });
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  const user = await User.findById(req.user.id);
  
  // Check if product is in wishlist
  if (!user.wishlist.includes(productId)) {
    return res.status(400).json({
      success: false,
      message: 'Product not in wishlist'
    });
  }

  // Check stock availability
  if (product.inventory.trackQuantity && product.inventory.quantity < quantity) {
    return res.status(400).json({
      success: false,
      message: 'Insufficient stock available'
    });
  }

  // Add to cart (assuming Cart model and logic exists)
  const Cart = (await import('../models/Cart.js')).default;
  const cart = await Cart.getOrCreateCart(req.user.id);
  
  let customizationData = { type: 'none', additionalPrice: 0 };
  if (customization && customization.type !== 'none') {
    if (!product.isCustomizable) {
      return res.status(400).json({
        success: false,
        message: 'This product is not customizable'
      });
    }
    customizationData = customization;
  }

  await cart.addItem(productId, quantity, customizationData);

  // Remove from wishlist
  user.wishlist = user.wishlist.filter(id => id.toString() !== productId);
  await user.save();

  res.json({
    success: true,
    message: 'Product moved to cart successfully',
    data: {
      productId,
      wishlistCount: user.wishlist.length,
      cartItemsCount: cart.totalItems
    }
  });
});

// @desc    Get wishlist recommendations
// @route   GET /api/wishlist/recommendations
// @access  Private
export const getWishlistRecommendations = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id).populate('wishlist');
  
  if (!user.wishlist || user.wishlist.length === 0) {
    // If no wishlist items, return popular products
    const popularProducts = await Product.find({ 
      isActive: true, 
      isFeatured: true 
    })
      .sort({ 'ratings.average': -1 })
      .limit(6)
      .select('name price comparePrice images category ratings');

    return res.json({
      success: true,
      data: {
        recommendations: popularProducts,
        reason: 'Popular products'
      }
    });
  }

  // Get categories from wishlist items
  const wishlistCategories = [...new Set(user.wishlist.map(item => item.category))];
  
  // Find similar products from same categories
  const recommendations = await Product.find({
    isActive: true,
    category: { $in: wishlistCategories },
    _id: { $nin: user.wishlist.map(item => item._id) }
  })
    .sort({ 'ratings.average': -1, isFeatured: -1 })
    .limit(6)
    .select('name price comparePrice images category ratings');

  res.json({
    success: true,
    data: {
      recommendations,
      reason: 'Based on your wishlist preferences'
    }
  });
});
