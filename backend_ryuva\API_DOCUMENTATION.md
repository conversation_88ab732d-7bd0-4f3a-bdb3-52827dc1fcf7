# Ryuva E-commerce API Documentation

## Overview
The Ryuva API provides a comprehensive backend solution for the handkerchief e-commerce platform, supporting both ready-made and customizable products.

## Base URL
```
http://localhost:3000/api
```

## Authentication
Most endpoints require authentication using Bearer tokens.

```
Authorization: Bearer <token>
```

For testing, use the sample token: `sample-token`

## API Endpoints

### Authentication

#### POST /api/auth/login
Login user and get authentication token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "customer"
    },
    "token": "sample-token"
  }
}
```

#### GET /api/auth/me
Get current user information (requires authentication).

### Products

#### GET /api/products
Get all products with pagination and filtering.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 12)
- `category` (optional): Filter by category
- `search` (optional): Search in name, description, and tags

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [...],
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 4,
      "pages": 1
    }
  }
}
```

#### GET /api/products/featured
Get featured products.

#### GET /api/products/:id
Get single product by ID.

#### GET /api/products/category/:category
Get products by category.

**Available Categories:**
- `heritage`
- `minimal-luxe`
- `wedding-gifting`
- `custom-monogram`

### Customization

#### GET /api/customization/:productId
Get customization options for a product.

**Response:**
```json
{
  "success": true,
  "data": {
    "productId": 1,
    "productName": "Heritage Paisley Rumaal",
    "basePrice": 899,
    "customizationOptions": {
      "monogram": {
        "enabled": true,
        "price": 150,
        "positions": ["bottom-right", "center"],
        "fonts": ["Script", "Block"],
        "colors": ["Gold", "Silver", "White"]
      }
    }
  }
}
```

#### POST /api/customization/:productId/calculate
Calculate total price with customizations.

**Request Body:**
```json
{
  "customizations": [
    {
      "type": "monogram",
      "details": {
        "text": "JD",
        "font": "Script",
        "color": "Gold",
        "position": "bottom-right"
      }
    }
  ]
}
```

### Wishlist (Requires Authentication)

#### GET /api/wishlist
Get user's wishlist.

#### POST /api/wishlist/:productId
Add product to wishlist.

#### DELETE /api/wishlist/:productId
Remove product from wishlist.

### Shopping Cart (Requires Authentication)

#### GET /api/cart
Get user's cart.

#### POST /api/cart/add
Add item to cart.

**Request Body:**
```json
{
  "productId": 1,
  "quantity": 2,
  "customization": {
    "type": "monogram",
    "details": {
      "text": "JD",
      "font": "Script",
      "color": "Gold",
      "position": "bottom-right"
    },
    "additionalPrice": 150
  }
}
```

#### DELETE /api/cart/item/:itemId
Remove item from cart.

### Orders (Requires Authentication)

#### POST /api/orders
Create new order.

**Request Body:**
```json
{
  "items": [
    {
      "productId": 1,
      "quantity": 1,
      "customization": {
        "type": "monogram",
        "additionalPrice": 150
      }
    }
  ],
  "shippingAddress": {
    "firstName": "John",
    "lastName": "Doe",
    "addressLine1": "123 Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "postalCode": "400001",
    "phone": "9876543210"
  },
  "paymentMethod": "stripe"
}
```

#### GET /api/orders
Get user's orders.

#### GET /api/orders/:id
Get single order by ID.

## Error Responses

All error responses follow this format:

```json
{
  "success": false,
  "message": "Error description"
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

## Sample Data

The API includes sample products for testing:

1. **Heritage Paisley Rumaal** (ID: 1) - ₹899
2. **Minimal Luxe Cotton** (ID: 2) - ₹599
3. **Wedding Elegance Set** (ID: 3) - ₹2,499
4. **Custom Monogram Classic** (ID: 4) - ₹799

## Testing

Use tools like Postman or curl to test the API endpoints. For authentication, use:

```
Authorization: Bearer sample-token
```

## Features

### Core E-commerce Features
- ✅ Product catalog with categories
- ✅ Search and filtering
- ✅ Shopping cart management
- ✅ Order processing
- ✅ User authentication
- ✅ Wishlist functionality

### Customization Features
- ✅ Product customization options
- ✅ Price calculation for customizations
- ✅ Monogram customization
- ✅ Fabric upgrades
- ✅ Custom embroidery

### Business Features
- ✅ Inventory tracking
- ✅ Order status management
- ✅ Shipping calculations
- ✅ Tax calculations (18% GST)
- ✅ Free shipping over ₹500

This API provides a solid foundation for the Ryuva handkerchief e-commerce platform with comprehensive support for both standard products and customizable items.
