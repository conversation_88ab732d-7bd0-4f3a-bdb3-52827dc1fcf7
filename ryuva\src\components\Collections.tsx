
import React from 'react'

const Collections: React.FC = () => {
  return (
    <section id="collections" className="py-16 md:py-24 bg-ryuva-cream scroll-animate">
      <div className="container mx-auto px-6">
        <h2 className="text-4xl font-serif font-bold text-ryuva-deep-green text-center mb-16">
          Discover Our Collections
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
            <img
              src="https://placehold.co/400x300/B08D57/FFFFFF?text=Heritage+Rumaal&font=playfairdisplay"
              alt="Heritage Rumaals"
              className="w-full h-56 object-cover"
            />
            <div className="p-6 flex flex-col flex-grow">
              <h3 className="text-2xl font-serif font-semibold text-ryuva-deep-green mb-2">
                Heritage Rumaals
              </h3>
              <p className="text-ryuva-charcoal text-sm mb-4 flex-grow">
                Timeless designs celebrating rich cultural patterns and artisanal craftsmanship.
              </p>
              <a href="#" className="mt-auto self-start text-ryuva-gold font-semibold hover:underline">
                Explore Heritage <span aria-hidden="true">&rarr;</span>
              </a>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
            <img
              src="https://placehold.co/400x300/36454F/FFFFFF?text=Minimal+Luxe&font=playfairdisplay"
              alt="Minimal Luxe Rumaals"
              className="w-full h-56 object-cover"
            />
            <div className="p-6 flex flex-col flex-grow">
              <h3 className="text-2xl font-serif font-semibold text-ryuva-deep-green mb-2">
                Minimal Luxe
              </h3>
              <p className="text-ryuva-charcoal text-sm mb-4 flex-grow">
                Clean lines, subtle textures, and sophisticated simplicity for the modern minimalist.
              </p>
              <a href="#" className="mt-auto self-start text-ryuva-gold font-semibold hover:underline">
                Shop Minimal Luxe <span aria-hidden="true">&rarr;</span>
              </a>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
            <img
              src="https://placehold.co/400x300/2E4034/FFFFFF?text=Wedding+Gifting&font=playfairdisplay"
              alt="Wedding Gifting Rumaals"
              className="w-full h-56 object-cover"
            />
            <div className="p-6 flex flex-col flex-grow">
              <h3 className="text-2xl font-serif font-semibold text-ryuva-deep-green mb-2">
                Wedding Gifting
              </h3>
              <p className="text-ryuva-charcoal text-sm mb-4 flex-grow">
                Elegant rumaals perfect for memorable wedding favors and bridal party gifts.
              </p>
              <a href="#" className="mt-auto self-start text-ryuva-gold font-semibold hover:underline">
                View Wedding Gifting <span aria-hidden="true">&rarr;</span>
              </a>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
            <img
              src="https://placehold.co/400x300/D1C0A8/333333?text=Custom+Monogram&font=playfairdisplay"
              alt="Custom Monogram Rumaals"
              className="w-full h-56 object-cover"
            />
            <div className="p-6 flex flex-col flex-grow">
              <h3 className="text-2xl font-serif font-semibold text-ryuva-deep-green mb-2">
                Custom Monogram
              </h3>
              <p className="text-ryuva-charcoal text-sm mb-4 flex-grow">
                Personalize your rumaal with bespoke monograms and unique designs.
              </p>
              <a href="#" className="mt-auto self-start text-ryuva-gold font-semibold hover:underline">
                Discover Monograms <span aria-hidden="true">&rarr;</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Collections