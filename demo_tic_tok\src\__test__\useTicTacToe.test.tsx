import { renderHook, act } from '@testing-library/react';
import { useTicTacToe } from '../components/useTicTacToe';

describe('useTicTacToe', () => {
  test('should initialize with empty board', () => {
    const { result } = renderHook(() => useTicTacToe());
    const [state] = result.current;
    
    expect(state.squares).toEqual(Array(9).fill(null));
    expect(state.isXNext).toBe(true);
    expect(state.status).toBe(false);
  });

  test('should update board when square is clicked', () => {
    const { result } = renderHook(() => useTicTacToe());
    
    act(() => {
      result.current[1].handleSquareClick(0);
    });
    
    expect(result.current[0].squares[0]).toBe('X');
    expect(result.current[0].isXNext).toBe(false);
  });

  test('should not update already filled square', () => {
    const { result } = renderHook(() => useTicTacToe());
    
    act(() => {
      result.current[1].handleSquareClick(0);
    });
    
    act(() => {
      result.current[1].handleSquareClick(0);
    });
    
    expect(result.current[0].squares[0]).toBe('X');
    expect(result.current[0].isXNext).toBe(false);
  });

  test('should detect X as winner in row', async () => {
    const { result } = renderHook(() => useTicTacToe());
    
    // X wins with top row
    act(() => { result.current[1].handleSquareClick(0); }); // X
    act(() => { result.current[1].handleSquareClick(3); }); // O
    act(() => { result.current[1].handleSquareClick(1); }); // X
    act(() => { result.current[1].handleSquareClick(4); }); // O
    act(() => { result.current[1].handleSquareClick(2); }); // X
    
    // Need to wait for the useEffect to run
    await new Promise(resolve => setTimeout(resolve, 0));
    
    expect(result.current[0].status).toBe('X');
  });

  test('should detect X as winner in column', async () => {
    const { result } = renderHook(() => useTicTacToe());
    
    // X wins with first column
    act(() => { result.current[1].handleSquareClick(0); }); // X
    act(() => { result.current[1].handleSquareClick(1); }); // O
    act(() => { result.current[1].handleSquareClick(3); }); // X
    act(() => { result.current[1].handleSquareClick(2); }); // O
    act(() => { result.current[1].handleSquareClick(6); }); // X
    
    // Need to wait for the useEffect to run
    await new Promise(resolve => setTimeout(resolve, 0));
    
    expect(result.current[0].status).toBe('X');
  });

  test('should detect X as winner in diagonal', async () => {
    const { result } = renderHook(() => useTicTacToe());
    
    // X wins with diagonal
    act(() => { result.current[1].handleSquareClick(0); }); // X
    act(() => { result.current[1].handleSquareClick(1); }); // O
    act(() => { result.current[1].handleSquareClick(4); }); // X
    act(() => { result.current[1].handleSquareClick(2); }); // O
    act(() => { result.current[1].handleSquareClick(8); }); // X
    
    // Need to wait for the useEffect to run
    await new Promise(resolve => setTimeout(resolve, 0));
    
    expect(result.current[0].status).toBe('X');
  });

  test('should detect O as winner', async () => {
    const { result } = renderHook(() => useTicTacToe());
    
    // O wins with middle column
    act(() => { result.current[1].handleSquareClick(0); }); // X
    act(() => { result.current[1].handleSquareClick(1); }); // O
    act(() => { result.current[1].handleSquareClick(2); }); // X
    act(() => { result.current[1].handleSquareClick(4); }); // O
    act(() => { result.current[1].handleSquareClick(6); }); // X
    act(() => { result.current[1].handleSquareClick(7); }); // O
    
    // Need to wait for the useEffect to run
    await new Promise(resolve => setTimeout(resolve, 0));
    
    expect(result.current[0].status).toBe('O');
  });

  test('should detect draw', async () => {
    const { result } = renderHook(() => useTicTacToe());
    
    // Draw game sequence
    act(() => { result.current[1].handleSquareClick(0); }); // X
    act(() => { result.current[1].handleSquareClick(4); }); // O
    act(() => { result.current[1].handleSquareClick(2); }); // X
    act(() => { result.current[1].handleSquareClick(1); }); // O
    act(() => { result.current[1].handleSquareClick(7); }); // X
    act(() => { result.current[1].handleSquareClick(3); }); // O
    act(() => { result.current[1].handleSquareClick(5); }); // X
    act(() => { result.current[1].handleSquareClick(8); }); // O
    act(() => { result.current[1].handleSquareClick(6); }); // X
    
    // Need to wait for the useEffect to run
    await new Promise(resolve => setTimeout(resolve, 0));
    
    expect(result.current[0].status).toBe('FULL');
  });

  test('should reset game state', () => {
    const { result } = renderHook(() => useTicTacToe());
    
    act(() => {
      result.current[1].handleSquareClick(0);
      result.current[1].handleSquareClick(1);
    });
    
    act(() => {
      result.current[1].resetGame();
    });
    
    expect(result.current[0].squares).toEqual(Array(9).fill(null));
    expect(result.current[0].isXNext).toBe(true);
    expect(result.current[0].status).toBe(false);
  });

  test('should not allow moves after game is won', async () => {
    const { result } = renderHook(() => useTicTacToe());
    
    // X wins with top row
    act(() => { result.current[1].handleSquareClick(0); }); // X
    act(() => { result.current[1].handleSquareClick(3); }); // O
    act(() => { result.current[1].handleSquareClick(1); }); // X
    act(() => { result.current[1].handleSquareClick(4); }); // O
    act(() => { result.current[1].handleSquareClick(2); }); // X wins
    
    // Need to wait for the useEffect to run
    await new Promise(resolve => setTimeout(resolve, 0));
    
    act(() => {
      result.current[1].handleSquareClick(5); // Should not update
    });
    
    expect(result.current[0].squares[5]).toBe(null);
  });
});