import React from 'react'

const Instagram: React.FC = () => {
  const instagramPosts = [
    { id: 1, image: "https://placehold.co/300x300/FDFBF5/333?text=Ryuva+Style+1", alt: "Instagram Post 1" },
    { id: 2, image: "https://placehold.co/300x300/B08D57/FFF?text=Ryuva+Style+2", alt: "Instagram Post 2" },
    { id: 3, image: "https://placehold.co/300x300/2E4034/FFF?text=Ryuva+Style+3", alt: "Instagram Post 3" },
    { id: 4, image: "https://placehold.co/300x300/36454F/FFF?text=Ryuva+Style+4", alt: "Instagram Post 4" },
    { id: 5, image: "https://placehold.co/300x300/D1C0A8/333?text=Ryuva+Style+5", alt: "Instagram Post 5" },
    { id: 6, image: "https://placehold.co/300x300/A07855/FFF?text=Ryuva+Style+6", alt: "Instagram Post 6" }
  ]

  return (
    <section id="instagram" className="py-16 md:py-24 bg-white scroll-animate">
      <div className="container mx-auto px-6 text-center">
        <h2 className="text-4xl font-serif font-bold text-ryuva-deep-green mb-4">
          Follow Our Journey #RyuvaStyle
        </h2>
        <p className="text-lg text-ryuva-charcoal mb-12">
          Inspiration on Instagram{' '}
          <a href="#" className="text-ryuva-gold hover:underline font-semibold">
            @RyuvaOfficial
          </a>
        </p>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-4 mb-12">
          {instagramPosts.map((post) => (
            <div key={post.id} className="aspect-square bg-ryuva-light-gray rounded-lg overflow-hidden group relative">
              <img 
                src={post.image} 
                alt={post.alt} 
                className="w-full h-full object-cover group-hover:opacity-75 transition-opacity"
              />
              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="white" className="w-10 h-10">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                </svg>
              </div>
            </div>
          ))}
        </div>
        
        <a 
          href="#" 
          className="bg-ryuva-deep-green text-white py-3.5 px-10 rounded-lg text-lg font-semibold hover:bg-opacity-90 transition-colors shadow-lg transform hover:scale-105"
        >
          Follow us on Instagram
        </a>
      </div>
    </section>
  )
}

export default Instagram
