import { render } from '@testing-library/react'
import App from '../App'
import TicTokToeGame from '../components/tictoe'
import { vi } from 'vitest'

// Mock the TicTokToeGame component
vi.mock('../components/tictoe', () => ({
  default: vi.fn(() => <div data-testid="mocked-tictoe">Mocked TicTacToe Game</div>)
}))

describe('App', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('renders TicTokToeGame component', () => {
    const { getByTestId } = render(<App />)
    
    // Check if TicTokToeGame is rendered
    expect(getByTestId('mocked-tictoe')).toBeInTheDocument()
    
    // Verify TicTokToeGame was called
    expect(TicTokToeGame).toHaveBeenCalled()
  })
})