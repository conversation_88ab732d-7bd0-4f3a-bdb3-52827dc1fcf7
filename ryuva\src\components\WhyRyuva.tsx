import React from 'react'

const WhyRyuva: React.FC = () => {
  return (
    <section id="why-ryuva" className="py-16 md:py-24 bg-white scroll-animate">
      <div className="container mx-auto px-6 text-center">
        <h2 className="text-4xl font-serif font-bold text-ryuva-deep-green mb-16">
          The Ryuva Difference
        </h2>
        
        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-10">
          <div className="flex flex-col items-center p-6 rounded-lg hover:shadow-lg transition-shadow">
            <div className="bg-ryuva-cream p-5 rounded-full mb-5 inline-block ring-2 ring-ryuva-gold/30">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-10 h-10 text-ryuva-gold">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 14.188V12.008L18.25 12zm-2.25 0h.008v.008h-.008V12z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-ryuva-deep-green mb-2">
              Premium Natural Fabrics
            </h3>
            <p className="text-ryuva-charcoal text-sm leading-relaxed">
              Indulge in the luxurious feel of carefully selected silks, cottons, and linens, chosen for their quality and comfort.
            </p>
          </div>
          
          <div className="flex flex-col items-center p-6 rounded-lg hover:shadow-lg transition-shadow">
            <div className="bg-ryuva-cream p-5 rounded-full mb-5 inline-block ring-2 ring-ryuva-gold/30">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-10 h-10 text-ryuva-gold">
                <path strokeLinecap="round" strokeLinejoin="round" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.82.61l-4.725-2.885a.563.563 0 00-.652 0L5.59 19.18a.562.562 0 01-.82-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-ryuva-deep-green mb-2">
              Handcrafted by Artisans
            </h3>
            <p className="text-ryuva-charcoal text-sm leading-relaxed">
              Each rumaal is a masterpiece, meticulously created by skilled Indian artisans, preserving traditional techniques.
            </p>
          </div>
          
          <div className="flex flex-col items-center p-6 rounded-lg hover:shadow-lg transition-shadow">
            <div className="bg-ryuva-cream p-5 rounded-full mb-5 inline-block ring-2 ring-ryuva-gold/30">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-10 h-10 text-ryuva-gold">
                <path strokeLinecap="round" strokeLinejoin="round" d="M21 11.25v8.25a1.5 1.5 0 01-1.5 1.5H5.25a1.5 1.5 0 01-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1014.625 7.5H12V4.875z" />
                <path strokeLinecap="round" strokeLinejoin="round" d="M18.75 11.25c0-2.278-1.684-4.125-3.75-4.125S11.25 8.972 11.25 11.25v8.25c0 .828.672 1.5 1.5 1.5h3c.828 0 1.5-.672 1.5-1.5v-8.25zM6.75 11.25c0-2.278-1.684-4.125-3.75-4.125S-.75 8.972-.75 11.25v8.25c0 .828.672 1.5 1.5 1.5h3c.828 0 1.5-.672 1.5-1.5v-8.25z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-ryuva-deep-green mb-2">
              Perfect for Gifting
            </h3>
            <p className="text-ryuva-charcoal text-sm leading-relaxed">
              An unforgettable gift that speaks volumes of thoughtfulness and refined taste for any occasion.
            </p>
          </div>
          
          <div className="flex flex-col items-center p-6 rounded-lg hover:shadow-lg transition-shadow">
            <div className="bg-ryuva-cream p-5 rounded-full mb-5 inline-block ring-2 ring-ryuva-gold/30">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-10 h-10 text-ryuva-gold">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.53 16.122a3 3 0 00-5.78 1.128 2.25 2.25 0 01-2.4 2.245 4.5 4.5 0 008.4-2.245c0-.399-.078-.78-.22-1.128zm0 0a15.998 15.998 0 003.388-1.62m-5.043-.025a15.994 15.994 0 011.622-3.395m3.42 3.42a15.995 15.995 0 004.764-4.648l3.876-5.814a1.151 1.151 0 00-1.597-1.597L14.146 6.32a15.996 15.996 0 00-4.649 4.763m3.42 3.42a6.776 6.776 0 00-3.42-3.42" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-ryuva-deep-green mb-2">
              Personalization Available
            </h3>
            <p className="text-ryuva-charcoal text-sm leading-relaxed">
              Add a unique touch with custom monograms, motifs, or messages to create a truly personal item.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default WhyRyuva
