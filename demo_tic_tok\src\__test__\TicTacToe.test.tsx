import { render, screen } from '@testing-library/react';
import TicTokToeGame from '../components/tictoe';
import { useTicTacToe } from '../components/useTicTacToe';
import { vi } from 'vitest';

// Mock the custom hook
vi.mock('../components/useTicTacToe');

describe('TicTokToeGame', () => {
  const mockHandleSquareClick = vi.fn();
  const mockResetGame = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders empty game board', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { squares: Array(9).fill(null), isXNext: true, status: false },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ]);

    const { container } = render(<TicTokToeGame />);
    
    const gameContainer = container.querySelector('.gamecontainer');
    expect(gameContainer).toBeInTheDocument();
    
    const squares = container.querySelectorAll('.flex.w-1\\/3');
    expect(squares.length).toBe(9);
  });

  test('handles square click', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { squares: Array(9).fill(null), isXNext: true, status: false },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ]);

    const { container } = render(<TicTokToeGame />);
    
    const squares = container.querySelectorAll('.flex.w-1\\/3');
    squares[0].dispatchEvent(new MouseEvent('click', { bubbles: true }));
    
    expect(mockHandleSquareClick).toHaveBeenCalledWith(0);
  });

  test('shows winner message when X wins', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { squares: ['X', 'X', 'X', null, null, null, null, null, null], isXNext: false, status: 'X' },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ]);

    render(<TicTokToeGame />);
    
    expect(screen.getByText(/X Won The Game/i)).toBeInTheDocument();
    
    const resetButton = screen.getByText(/Play Again/i);
    resetButton.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    
    expect(mockResetGame).toHaveBeenCalled();
  });

  test('shows draw message when game is a draw', () => {
    vi.mocked(useTicTacToe).mockReturnValue([
      { 
        squares: ['X', 'O', 'X', 'X', 'O', 'X', 'O', 'X', 'O'], 
        isXNext: true, 
        status: 'FULL' 
      },
      { handleSquareClick: mockHandleSquareClick, resetGame: mockResetGame }
    ]);

    render(<TicTokToeGame />);
    
    expect(screen.getByText(/It's a Draw/i)).toBeInTheDocument();
  });
});