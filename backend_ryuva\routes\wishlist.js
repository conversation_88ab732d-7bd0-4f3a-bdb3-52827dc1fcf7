import express from 'express';
import {
  getWishlist,
  addToWishlist,
  removeFromWishlist,
  clearWishlist,
  checkWishlistStatus,
  moveToCart,
  getWishlistRecommendations
} from '../controllers/wishlistController.js';
import { protect } from '../middleware/auth.js';
import { param, body } from 'express-validator';
import { validate } from '../middleware/validation.js';

const router = express.Router();

// Validation rules
const productIdValidation = [
  param('productId')
    .isMongoId()
    .withMessage('Invalid product ID')
];

const moveToCartValidation = [
  param('productId')
    .isMongoId()
    .withMessage('Invalid product ID'),
  body('quantity')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Quantity must be between 1 and 10'),
  body('customization.type')
    .optional()
    .isIn(['none', 'monogram', 'embroidery', 'fabric-upgrade'])
    .withMessage('Invalid customization type')
];

// All wishlist routes require authentication
router.use(protect);

// Routes
router.get('/', getWishlist);
router.get('/recommendations', getWishlistRecommendations);
router.get('/check/:productId', productIdValidation, validate, checkWishlistStatus);
router.post('/:productId', productIdValidation, validate, addToWishlist);
router.post('/:productId/move-to-cart', moveToCartValidation, validate, moveToCart);
router.delete('/:productId', productIdValidation, validate, removeFromWishlist);
router.delete('/', clearWishlist);

export default router;
