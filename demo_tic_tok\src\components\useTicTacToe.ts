import { useState, useEffect } from 'react';

type Player = 'X' | 'O' | null;
export type GameStatus = 'X' | 'O' | 'FULL' | false;

export interface TicTacToeState {
  squares: Player[];
  isXNext: boolean;
  status: GameStatus;
}

export interface TicTacToeActions {
  handleSquareClick: (index: number) => void;
  resetGame: () => void;
}

export function useTicTacToe(): [TicTacToeState, TicTacToeActions] {
  const [squares, setSquares] = useState<Player[]>(Array(9).fill(null));
  const [isXNext, setIsXNext] = useState<boolean>(true);
  const [status, setStatus] = useState<GameStatus>(false);

  const winningConditions = [
    [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
    [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
    [0, 4, 8], [2, 4, 6]             // diagonals
  ];

  const checkWinner = (): Player => {
    for (let logic of winningConditions) {
      const [a, b, c] = logic;
      if (squares[a] !== null && squares[a] === squares[b] && squares[a] === squares[c]) {
        return squares[a];
      }
    }
    return null;
  };

  const isBoardFull = (): boolean => {
    return squares.every(square => square === 'X' || square === 'O');
  };

  const handleSquareClick = (index: number): void => {
    // Don't allow clicks on filled squares or after game is over
    if (squares[index] !== null || status) return;
    
    const newSquares = [...squares];
    newSquares[index] = isXNext ? 'X' : 'O';
    
    setSquares(newSquares);
    setIsXNext(!isXNext);
  };

  const resetGame = (): void => {
    setSquares(Array(9).fill(null));
    setIsXNext(true);
    setStatus(false);
  };

  // Check for winner or draw after each move
  useEffect(() => {
    const winner = checkWinner();
    if (winner) {
      setStatus(winner);
    } else if (isBoardFull()) {
      setStatus('FULL');
    }
  }, [squares]);

  return [
    { 
      squares, 
      isXNext, 
      status
    },
    { 
      handleSquareClick, 
      resetGame 
    }
  ];
}